<?php

  namespace classes;
  trait DatesTrait {

    private function getStartAndEndDate(string $date, string $view): array {
      if ($view === 'week') {
        $start = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $end = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
      }
      else {
        $start = date('Y-m-01', strtotime($date));
        $end = date('Y-m-t', strtotime($date));

        if (date('N', strtotime($start)) != 1) {
          $start = date('Y-m-d', strtotime('last monday', strtotime($start)));
        }

        if (date('N', strtotime($end)) != 7) {
          $end = date('Y-m-d', strtotime('next sunday', strtotime($end)));
        }
      }

      return ['start' => $start, 'end' => $end];
    }
  }