<script setup>
import { computed } from 'vue';
import { employeeAvailability } from '@/helpers/constants.js';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  availabilityData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue']);

const closeDialog = () => {
  emit('update:modelValue', false);
};

// Calculate total available employees
const totalAvailable = computed(() => {
  if (!props.availabilityData.availability_counts) return 0;
  const counts = props.availabilityData.availability_counts;
  return (counts.available || 0) + 
         (counts.available_morning || 0) + 
         (counts.available_afternoon || 0) + 
         (counts.available_morning_afternoon || 0);
});

const totalEmployees = computed(() => {
  if (!props.availabilityData.employee_details) return 0;
  return props.availabilityData.employee_details.length;
});

// Group employees by their availability status
const groupedEmployees = computed(() => {
  if (!props.availabilityData.employee_details) return [];
  
  const groups = {};
  
  // Group employees by their availability
  props.availabilityData.employee_details.forEach(employee => {
    const availabilityType = employeeAvailability.find(type => type.value === employee.availability);
    
    if (availabilityType) {
      if (!groups[employee.availability]) {
        groups[employee.availability] = {
          ...availabilityType,
          employees: []
        };
      }
      groups[employee.availability].employees.push(employee);
    }
  });
  
  // Return only groups that have employees
  return Object.values(groups).filter(group => group.employees.length > 0);
});
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="closeDialog"
    max-width="600"
  >
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span>Beschikbaarheid {{ props.availabilityData.date }}</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="closeDialog"
        />
      </v-card-title>
      
      <v-card-text>
        <div class="mb-4">
          <v-chip
            color="primary"
            variant="outlined"
            class="mr-2"
          >
            {{ totalAvailable }} van {{ totalEmployees }} beschikbaar
          </v-chip>
        </div>

        <v-list>
          <template
            v-for="group in groupedEmployees"
            :key="group.value"
          >
            <v-divider class="my-2" />
            <v-list-item slim>
              <template #prepend>
                <v-icon
                  :icon="group.icon"
                  :color="group.color"
                />
              </template>
              <v-list-item-title>
                <strong>
                  {{ group.title }} ({{ group.employees.length }})
                </strong>
              </v-list-item-title>
              <div class="d-flex flex-wrap ga-2 mt-2">
                <v-chip
                  v-for="employee in group.employees"
                  :key="employee.name"
                  size="small"
                  variant="outlined"
                  :color="group.color"
                >
                  {{ employee.name }}
                </v-chip>
              </div>
            </v-list-item>
          </template>
        </v-list>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
