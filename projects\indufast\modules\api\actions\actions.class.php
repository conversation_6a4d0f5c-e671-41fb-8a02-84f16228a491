<?php

  use classes\ApiResponse;
  use classes\DatesTrait;
  use classes\TimesTrait;
  use domain\accredis\service\AccredisDriverService;

  require_once 'ApiAuthenticationTrait.php';
  require_once 'IndufastProjectApiTrait.php';
  require_once 'IndufastEmployeeApiTrait.php';
  require_once 'IndufastCalendarEventApiTrait.php';
  require_once 'IndufastCalendarEventEmployeeApiTrait.php';
  require_once 'IndufastEmployeeAvailabilityApiTrait.php';
  require_once 'IndufastChangelogApiTrait.php';
  require_once 'IndufastGoogleApiTrait.php';

  class apiIndufastActions extends apiActions {

    use ApiAuthenticationTrait;
    use IndufastProjectApiTrait;
    use IndufastEmployeeApiTrait;
    use IndufastCalendarEventApiTrait;
    use IndufastCalendarEventEmployeeApiTrait;
    use IndufastEmployeeAvailabilityApiTrait;
    use IndufastChangelogApiTrait;
    use IndufastGoogleApiTrait;
    use ApiRequestValidateTrait;
    use TimesTrait;
    use DatesTrait;

    const ACTION_PERMISSION = [
      'login'  => null,
      'logout' => null,

      'getUsers'          => 'M_ACCREDIS',
      'getUser'           => 'M_ACCREDIS',
      'createWorkdayLine' => 'M_ACCREDIS',
      'getWorkdays'       => 'M_ACCREDIS',
      'calculateWorkday'  => 'M_ACCREDIS',
      'getWorkdaySummary' => 'M_ACCREDIS',
      'updateWorkday'     => 'M_ACCREDIS',

      'projectCreate' => 'M_ADMINISTER_PROJECTS',
      'projectUpdate' => 'M_ADMINISTER_PROJECTS',
      'projectList'   => 'M_ADMINISTER_PROJECTS',
      'projectDelete' => 'M_ADMINISTER_PROJECTS',

      'employeeCreate' => 'M_ADMINISTER_EMPLOYEES',
      'employeeUpdate' => 'M_ADMINISTER_EMPLOYEES',
      'employeeList'   => 'M_ADMINISTER_EMPLOYEES',
      'employeeDelete' => 'M_ADMINISTER_EMPLOYEES',

      'eventCreate' => 'M_ADMINISTER_PROJECTS',
      'eventUpdate' => 'M_ADMINISTER_PROJECTS',
      'eventList'   => 'M_ADMINISTER_PROJECTS',
      'eventDelete' => 'M_ADMINISTER_PROJECTS',

      'eventEmployeeCreate' => 'M_ADMINISTER_PROJECTS',
      'eventEmployeeDelete' => 'M_ADMINISTER_PROJECTS',

      'employeeAvailabilityList'       => 'M_ADMINISTER_PROJECTS',
      'employeeAvailabilityUpdate'     => 'M_ADMINISTER_PROJECTS',
      'employeeAvailabilityForProject' => 'M_ADMINISTER_PROJECTS',
      'employeeAvailabilityRange'      => 'M_ADMINISTER_PROJECTS',

      'searchLocations'                => 'M_ADMINISTER_PROJECTS',

      'changelog' => 'M_CHANGELOG',
    ];

    public function __construct() {
      $this->data = RequestHelper::getInputFileContents();
    }

    public function preExecute(): void {
      if (!array_key_exists($this->action, self::ACTION_PERMISSION)) {
        ApiResponse::sendResponseError('Invalid action');
      }

      if (self::ACTION_PERMISSION[$this->action]) {
        if (empty($_SESSION['loggedIn']) || empty($_SESSION['userObject'])) {
          ApiResponse::sendResponseUnauthorized();
        }
        elseif (!Privilege::hasRight(self::ACTION_PERMISSION[$this->action])) {
          ApiResponse::sendAccessDeniedResponse();
        }
      }
    }

    public function executeGetUsers(): void {
      $drivers = [];
      foreach (AccredisDriverService::getAll() as $driver) {
        $name = $driver['user']->getNaam();
        $drivers[$name] = [
          'name'          => $name,
          'accredis_name' => $driver['accredis-driver-name'],
          'id'            => (int)$driver['user']->id,
        ];
      }

      ksort($drivers);
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, array_values($drivers));
    }

    public function executeGetUser(): void {
      if (!$userId = $_GET['user_id'] ?? null) {
        ApiResponse::sendResponseError("No user_id specified");
      }

      $user = User::find_by(['id' => $userId]);
      $name = \UserProfile::find_by(['user_id' => $userId, 'code' => 'accredis-driver-name'])->value;

      if (!$user || !$name) {
        ApiResponse::sendResponseError("Invalid user_id specified");
      }

      $driver = [
        'name'          => $user->getNaam(),
        'accredis_name' => $name,
        'id'            => (int)$user->id,
      ];

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $driver);
    }

    public function executeCreateWorkdayLine(): void {

      // Check input.
      if (!$workdayId = $_GET['workday_id'] ?? null) {
        ApiResponse::sendResponseError("No workday_id specified");
      }
      if (!$workday = IndufastWorkday::find_by(['id' => $workdayId])) {
        ApiResponse::sendResponseError("Invalid workday_id specified");
      }

      $workdayLine = new IndufastWorkdayLine();
      $workdayLine->workday_id = $workday->id;
      $workdayLine->start = $this->data->start;
      $workdayLine->end = $this->data->end;
      $workdayLine->remark = (trim($this->data->remark ?? '')) ? trim($this->data->remark) : null;
      $workdayLine->save();

      $workday->calculate();
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeGetWorkdays(): void {
      if (!$userId = $_GET['user_id'] ?? null) {
        ApiResponse::sendResponseError("No user_id specified");
      }
      $month = $_GET['month'] ?? date('M');
      $year = $_GET['year'] ?? date('Y');
      $workdays = IndufastWorkday::findAllByMonth($userId, $year, $month);
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workdays);
    }

    /**
     * @throws Exception
     */
    public function executeCalculateWorkday(): void {

      // Check input.
      if (!$workdayId = $_GET['workday_id'] ?? null) {
        ApiResponse::sendResponseError("No workday_id specified");
      }
      if (!$workday = IndufastWorkday::find_by(['id' => $workdayId])) {
        ApiResponse::sendResponseError("Invalid workday_id specified");
      }
      if (!$input = json_decode(file_get_contents('php://input'), true)) {
        ApiResponse::sendResponseError("No JSON body provided");
      }

      // Parse input.
      $workday->travel_to_end_line_id = $input['selectedTravelToEndLineId'] ?? null;
      $workday->travel_from_start_line_id = $input['selectedTravelFromStartLineId'] ?? null;
      $workdayLinesData = $input['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        $workdayLine->void = ($workdayLinesData[$workdayLine->id]['void'] ?? false);
      }

      // Calculate workday.
      $workday->calculate();

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

    public function executeGetWorkdaySummary(): void {
      if (!$userId = $_GET['user_id'] ?? null) {
        ApiResponse::sendResponseError("No user_id specified");
      }
      // Parse input.
      $month = $_GET['month'] ?? date('M');
      $year = $_GET['year'] ?? date('Y');

      $workdays = IndufastWorkday::findAllByMonth($userId, $year, $month);
      foreach ($workdays as $workday) {
        $workday->calculate();
      }

      // Send response.
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, [
        'count'                    => count($workdays),
        'total'                    => $this->addTimes(array_filter(array_column($workdays, 'workdayDuration'))),
        'totalOutsideWorkingHours' => $this->addTimes(array_filter(array_column($workdays, 'workdayOutsideWorkingHours'))),
        'totalTravel'              => $this->addTimes(array_filter(array_column($workdays, 'travelDurationNet'))),
      ]);
    }

    public function executeUpdateWorkday(): void {

      // Check input.
      if (!$workdayId = $_GET['workday_id'] ?? null) {
        ApiResponse::sendResponseError("No workday_id specified");
      }
      if (!$workday = IndufastWorkday::find_by(['id' => $workdayId])) {
        ApiResponse::sendResponseError("Invalid workday_id specified");
      }
      if (!$input = json_decode(file_get_contents('php://input'), true)) {
        ApiResponse::sendResponseError("Invalid JSON body provided");
      }
      if (empty($input['status']) || !in_array($input['status'], ['new', 'processed'])) {
        ApiResponse::sendResponseError("Invalid status provided");
      }

      // Parse input.
      if (!empty($input['selectedTravelToEndLineId'])) {
        $workday->travel_to_end_line_id = (is_numeric($input['selectedTravelToEndLineId'])) ? (int)$input['selectedTravelToEndLineId'] : null;
      }
      if (!empty($input['selectedTravelFromStartLineId'])) {
        $workday->travel_from_start_line_id = (is_numeric($input['selectedTravelFromStartLineId'])) ? (int)$input['selectedTravelFromStartLineId'] : null;
      }
      $workday->remark = (trim($input['remark'] ?? false)) ? trim($input['remark']) : null;
      $workday->status = $input['status'];

      // Save workday lines.
      $workdayLinesData = $input['workdayLinesData'] ?? [];
      foreach ($workday->lines() as $workdayLine) {
        if (!empty($workdayLinesData[$workdayLine->id])) {
          $workdayLine->void = ($workdayLinesData[$workdayLine->id]['void'] ?? false) ? 1 : 0;
          $workdayLine->updateTS = date("Y-m-d H:i:s");
          $workdayLine->save();
        }
      }

      // Save and return workday.
      $workday->updateTS = date("Y-m-d H:i:s");
      $workday->save();
      $workday->calculate();
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $workday);
    }

  }
