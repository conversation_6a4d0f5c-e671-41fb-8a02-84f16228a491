<?php

  use classes\ApiResponse;
  use domain\project\service\ProjectUpdater;

  trait IndufastEmployeeAvailabilityApiTrait {
    use PropertyCastTrait;

    public function executeEmployeeAvailabilityList(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
      ];
      $data = $this->validateRequest($_GET, $rules)->getValidatedData();

      $result = [];
      try {
        $employees = IndufastEmployee::find_all_by(['active' => true]);
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $data['date']);

        foreach ($employees as $employee) {
          $events = $eventsByEmployee[$employee->id] ?? [];
          $availability = $employee->calculateAvailabilityFromEvents($events, $data['date'], true);

          $result[] = [
            'employee'     => $employee,
            'availability' => $availability,
            'events'       => $events,
          ];
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }

    public function executeEmployeeAvailabilityUpdate(): void {
      try {
        // Updates employee availability and project validity
        ProjectUpdater::updateProjectValidity();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK);
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }
    }

    public function executeEmployeeAvailabilityForProject(): void {
      $rules = [
        'employee_id' => 'required|integer',
        'project_id' => 'required|integer',
        'exclude_event_id' => 'integer',
      ];
      $data = $this->validateRequest($_GET, $rules)->getValidatedData();

      try {
        $project = IndufastProject::find_by_id($data['project_id']);
        if (!$project) {
          throw new \Exception('Project not found');
        }

        $employee = IndufastEmployee::find_by_id($data['employee_id']);
        if (!$employee) {
          throw new \Exception('Employee not found');
        }

        $events = [];
        foreach ($project->events() as $event) {
          if ($data['exclude_event_id'] && $event->id == $data['exclude_event_id']) {
            continue;
          }

          $date = date('Y-m-d', strtotime($event->start));
          $availability = $employee->getAvailability($date, eventToIgnore: $event);
          $events[] = [
            'event' => $event,
            'availability' => $availability,
            'conflict' => $employee->hasConflict($event, $availability),
          ];
        }

      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $events);
    }

    public function executeEmployeeAvailabilityRange(): void {
      $rules = [
        'date' => 'required|date:Y-m-d',
        'view' => 'required|string|in:week,month',
      ];
      $validation = $this->validateRequest($_GET, $rules);
      $data = $validation->getValidatedData();

      ['start' => $start, 'end' => $end] = $this->getStartAndEndDate($data['date'], $data['view']);

      $result = [];
      try {
        $employees = IndufastEmployee::find_all_by(['active' => true]);
        // Get all calendar events for all employees in the specified date range
        $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($employees, $start, $end);

        // Generate availability for each day in the range
        $currentDate = new DateTime($start);
        $endDate = new DateTime($end);

        while ($currentDate <= $endDate) {
          $dateString = $currentDate->format('Y-m-d');

          // Collect availability data for this day
          $availabilityCounts = [
            'available' => 0,
            'available_morning' => 0,
            'available_afternoon' => 0,
            'available_morning_afternoon' => 0,
            'not_available' => 0,
            'not_available_error' => 0,
          ];

          $employeeDetails = [];

          foreach ($employees as $employee) {
            $employeeEvents = $eventsByEmployee[$employee->id] ?? [];

            // Filter events for the current date
            $dayEvents = array_filter($employeeEvents, fn ($event) => date('Y-m-d', strtotime($event->start->dateTime ?? $event->start->date)) === $dateString);

            $availability = $employee->calculateAvailabilityFromEvents($dayEvents, $dateString);

            // Count availability statuses
            if (isset($availabilityCounts[$availability])) {
              $availabilityCounts[$availability]++;
            }

            // Store employee details for description
            $employeeDetails[] = [
              'name' => $employee->name ?? $employee->email ?? 'Unknown',
              'availability' => $availability,
            ];
          }

          // Build the data object for this day (raw data for front-end formatting)
          $result[] = [
            'date' => $dateString,
            'availability_counts' => $availabilityCounts,
            'employee_details' => $employeeDetails,
            'type' => 'availability',
          ];

          $currentDate->modify('+1 day');
        }
      } catch (\Exception $e) {
        ApiResponse::sendResponseError($e->getMessage());
      }

      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $result);
    }


  }